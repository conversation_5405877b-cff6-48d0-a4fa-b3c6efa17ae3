# Custom Backend Domain Testing Guide

This guide helps you test and verify that your custom backend domain `https://api.talktoora.com` is working correctly with your ORA application.

## Pre-Testing Checklist

Before testing, ensure you have completed:

- ✅ DNS CNAME record added for `api.talktoora.com`
- ✅ SSL certificate is active (green in Cloud Console)
- ✅ Environment variables updated in code
- ✅ Application deployed with new configuration
- ✅ OAuth configuration updated in Google Console

## Testing Steps

### Step 1: Basic Connectivity Tests

**Test Backend Domain:**
```bash
# Test if domain resolves
nslookup api.talktoora.com

# Test HTTPS connectivity
curl -I https://api.talktoora.com

# Test health endpoint
curl https://api.talktoora.com/health
```

**Expected Results:**
- Domain resolves to Google's IP addresses
- HTTPS returns 200 status
- Health endpoint returns JSON with `"status": "healthy"`

### Step 2: API Endpoint Tests

**Test Authentication Endpoints:**
```bash
# Test OAuth initiation
curl -I https://api.talktoora.com/api/auth/google

# Test API status
curl https://api.talktoora.com/api/auth/status
```

**Expected Results:**
- <PERSON>Auth endpoint redirects to Google (302 status)
- API endpoints return proper JSON responses

### Step 3: Frontend Integration Tests

**Test Frontend to Backend Communication:**

1. **Open Browser Developer Tools**
   - Go to https://talktoora.com
   - Open DevTools → Network tab

2. **Check API Calls**
   - Look for requests to `api.talktoora.com`
   - Verify no CORS errors
   - Check that authentication flows work

3. **Test Authentication Flow**
   - Click "Sign in with Google"
   - Verify redirect goes to `api.talktoora.com`
   - Check OAuth consent screen shows "ORA"
   - Confirm successful login

### Step 4: WebSocket Connection Tests

**Test Real-time Features:**

1. **Login to Application**
   - Go to https://talktoora.com
   - Complete authentication

2. **Test Voice Chat**
   - Navigate to `/chat`
   - Start a voice conversation
   - Verify WebSocket connects to `wss://api.talktoora.com`

3. **Check Browser Console**
   - Look for WebSocket connection messages
   - Verify no connection errors

## Verification Commands

### Quick Health Check
```bash
# All-in-one health check
curl -s https://api.talktoora.com/health | jq '.'
```

### OAuth Flow Test
```bash
# Test OAuth redirect (should return 302)
curl -I https://api.talktoora.com/api/auth/google
```

### CORS Test
```bash
# Test CORS headers
curl -H "Origin: https://talktoora.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://api.talktoora.com/api/auth/status
```

## Expected Behavior

### ✅ Success Indicators

1. **Domain Resolution**
   - `api.talktoora.com` resolves to Google Cloud IPs
   - SSL certificate is valid and trusted

2. **API Responses**
   - Health endpoint returns healthy status
   - Authentication endpoints work correctly
   - CORS headers allow frontend domain

3. **OAuth Flow**
   - Consent screen shows "ORA" (not domain name)
   - Redirects work correctly
   - Authentication completes successfully

4. **Application Functionality**
   - Login/logout works
   - Voice chat connects
   - All features function normally

### ❌ Common Issues

1. **DNS Not Propagated**
   - Domain doesn't resolve
   - SSL certificate pending
   - **Solution**: Wait 5-15 minutes, check DNS propagation

2. **CORS Errors**
   - Browser console shows CORS errors
   - API calls fail from frontend
   - **Solution**: Verify CORS_ORIGINS environment variable

3. **OAuth Errors**
   - "redirect_uri_mismatch" error
   - Authentication fails
   - **Solution**: Check OAuth redirect URIs configuration

4. **SSL Issues**
   - Certificate warnings
   - Mixed content errors
   - **Solution**: Wait for SSL provisioning, check certificate status

## Troubleshooting Commands

### Check DNS Propagation
```bash
# Check from multiple locations
dig api.talktoora.com
nslookup api.talktoora.com *******
```

### Check SSL Certificate
```bash
# Verify SSL certificate
openssl s_client -connect api.talktoora.com:443 -servername api.talktoora.com
```

### Check Cloud Run Service
```bash
# Check service status
gcloud run services describe ora-backend --region=us-central1

# Check domain mapping
gcloud beta run domain-mappings describe api.talktoora.com --region=us-central1
```

### Check Application Logs
```bash
# View backend logs
gcloud logs read --service=ora-backend --region=us-central1 --limit=20

# View frontend logs
gcloud logs read --service=ora-frontend --region=us-central1 --limit=20
```

## Performance Verification

### Response Time Tests
```bash
# Test API response time
time curl -s https://api.talktoora.com/health > /dev/null

# Test frontend load time
time curl -s https://talktoora.com > /dev/null
```

### Load Testing (Optional)
```bash
# Simple load test (requires apache2-utils)
ab -n 100 -c 10 https://api.talktoora.com/health
```

## Final Verification Checklist

- [ ] `https://api.talktoora.com/health` returns healthy status
- [ ] `https://talktoora.com` loads correctly
- [ ] Google OAuth shows "ORA" branding
- [ ] Authentication flow completes successfully
- [ ] Voice chat functionality works
- [ ] No CORS errors in browser console
- [ ] WebSocket connections establish correctly
- [ ] All API endpoints respond properly

## Support

If you encounter issues:

1. **Check the troubleshooting section above**
2. **Review Cloud Console for errors**
3. **Check application logs**
4. **Verify DNS and SSL status**
5. **Test with different browsers/incognito mode**

---

**Success**: When all tests pass, your custom backend domain integration is complete and your application will show professional "ORA" branding instead of technical URLs!
