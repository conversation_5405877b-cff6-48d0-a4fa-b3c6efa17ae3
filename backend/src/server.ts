import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import session from 'express-session';
import connectPgSimple from 'connect-pg-simple';
import { createServer } from 'http';
import dotenv from 'dotenv';

// Import database and services
import { testConnection, closeDatabase, pool } from './database/config.js';
import { ChatWebSocketServer } from './websocket/chatServer.js';
import { voiceProcessorService } from './services/voiceProcessor.js';

// Import routes
import authRoutes from './api/auth.js';
import chatRoutes from './api/chat.js';
import voiceRoutes from './api/voice.js';
import adminRoutes from './api/admin.js';
import voiceControlRoutes from './routes/voiceControl.js';
import { initializeDefaultAdmin } from './middleware/adminAuth.js';

// Import middleware
import { authMiddleware } from './middleware/auth.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Create HTTP server
const server = createServer(app);

// Session store
const PgSession = connectPgSimple(session);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: { policy: "same-origin-allow-popups" }
}));

// CORS configuration
const corsOrigins = process.env.CORS_ORIGINS
  ? process.env.CORS_ORIGINS.split(/[,\s]+/).filter(Boolean)
  : [process.env.FRONTEND_URL || 'http://localhost:3000'];

app.use(cors({
  origin: corsOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Access-Control-Allow-Origin']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.'
    },
    timestamp: new Date()
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan(NODE_ENV === 'production' ? 'combined' : 'dev'));

// Session middleware
app.use(session({
  store: new PgSession({
    pool: pool,
    tableName: 'session'
  }),
  secret: process.env.SESSION_SECRET || 'your-session-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Health check endpoint
app.get('/health', async (req, res) => {
  let dbStatus = 'unknown';
  try {
    await testConnection();
    dbStatus = 'connected';
  } catch (error) {
    dbStatus = 'disconnected';
  }

  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date(),
      environment: NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      database: dbStatus,
      port: PORT
    }
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/voice', voiceRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/voice-control', voiceControlRoutes);

// WebSocket status endpoint
app.get('/api/ws/status', authMiddleware.optionalAuthenticate, (req, res) => {
  const stats = chatWebSocketServer.getStats();
  res.json({
    success: true,
    data: {
      websocket: {
        ...stats,
        connectedUsers: req.user ? stats.connectedUsers : stats.connectedUsers.length
      }
    },
    timestamp: new Date()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`
    },
    timestamp: new Date()
  });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);
  
  res.status(error.statusCode || 500).json({
    success: false,
    error: {
      code: error.code || 'INTERNAL_SERVER_ERROR',
      message: NODE_ENV === 'production' 
        ? 'An internal server error occurred' 
        : error.message || 'Internal server error',
      ...(NODE_ENV !== 'production' && { stack: error.stack })
    },
    timestamp: new Date()
  });
});

// Initialize WebSocket server
let chatWebSocketServer: ChatWebSocketServer;

// Graceful shutdown handler
const gracefulShutdown = async (signal: string) => {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Stop voice processor service
    try {
      voiceProcessorService.stopBackgroundProcessing();
      console.log('🎵 Voice processor service stopped');
    } catch (error) {
      console.warn('⚠️ Error stopping voice processor service:', error);
    }

    // Close WebSocket server
    if (chatWebSocketServer) {
      chatWebSocketServer.close();
    }

    // Close HTTP server
    server.close(() => {
      console.log('📦 HTTP server closed');
    });

    // Close database connections
    await closeDatabase();
    
    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
async function startServer() {
  try {
    // Test database connection (non-blocking for Cloud Run health checks)
    try {
      await testConnection();
    } catch (dbError) {
      console.warn('⚠️ Database connection failed, but server will start anyway:', dbError);
      console.warn('⚠️ Database-dependent features will not work until connection is established');
    }

    // Start HTTP server
    server.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT} in ${NODE_ENV} mode`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`🔐 Auth endpoint: http://localhost:${PORT}/api/auth`);
      console.log(`💬 Chat API: http://localhost:${PORT}/api/chat`);
    });

    // Initialize WebSocket server
    chatWebSocketServer = new ChatWebSocketServer(server, '/ws/chat');
    console.log(`🔌 WebSocket server: ws://localhost:${PORT}/ws/chat`);

    // Initialize voice processor service (non-blocking)
    try {
      await voiceProcessorService.initialize();
      console.log(`🎵 Voice processor service initialized`);
    } catch (voiceError) {
      console.warn('⚠️ Voice processor initialization failed, but server will continue:', voiceError);
      console.warn('⚠️ Voice storage features will not work until service is initialized');
    }

    // Initialize default admin if none exist (non-blocking)
    try {
      const defaultAdminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
      await initializeDefaultAdmin(defaultAdminEmail);
    } catch (adminError) {
      console.warn('⚠️ Admin initialization failed, but server will continue:', adminError);
    }

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

export { app, server };
