# Google OAuth Configuration for Custom Domain

After setting up your custom domain `talktoora.com`, you need to update your Google OAuth configuration to allow authentication from the new domain.

## Step-by-Step OAuth Update

### Step 1: Access Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project: `ora-phase1`
3. Navigate to "APIs & Services" → "Credentials"

### Step 2: Update OAuth 2.0 Client

1. **Find Your OAuth Client**
   - Look for the OAuth 2.0 Client ID you're using
   - Current Client ID: `222129249954-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com`

2. **Edit the Client**
   - Click on the client name to edit it

3. **Update Authorized JavaScript Origins**
   Add these origins:
   ```
   https://talktoora.com
   https://ora-frontend-222129249954.us-central1.run.app
   ```

4. **Update Authorized Redirect URIs**
   Add these redirect URIs:
   ```
   https://talktoora.com/auth/callback
   https://talktoora.com/auth/google/callback
   https://ora-frontend-222129249954.us-central1.run.app/auth/callback
   https://ora-frontend-222129249954.us-central1.run.app/auth/google/callback
   ```

5. **Save Changes**
   - Click "Save" to apply the changes

### Step 3: Verify Configuration

Your OAuth client should now have:

**Authorized JavaScript Origins:**
- `https://talktoora.com`
- `https://ora-frontend-222129249954.us-central1.run.app`

**Authorized Redirect URIs:**
- `https://talktoora.com/auth/callback`
- `https://talktoora.com/auth/google/callback`
- `https://ora-frontend-222129249954.us-central1.run.app/auth/callback`
- `https://ora-frontend-222129249954.us-central1.run.app/auth/google/callback`

## Important Notes

1. **Keep Both Domains**: Keep both the original Cloud Run URL and the custom domain for redundancy during the transition.

2. **No Code Changes Needed**: The OAuth client ID remains the same, so no code changes are required.

3. **Propagation Time**: OAuth changes are usually immediate, but can take up to 5 minutes to propagate.

4. **Testing**: Test authentication on both domains to ensure everything works.

## Testing Authentication

After making these changes, test the authentication flow:

1. **Original Domain**: https://ora-frontend-222129249954.us-central1.run.app
2. **Custom Domain**: https://talktoora.com (after DNS setup is complete)

Both should work for Google SSO login.

## Troubleshooting

### "redirect_uri_mismatch" Error
- Ensure all redirect URIs are added to the OAuth client
- Check that the domain matches exactly (including https://)
- Wait a few minutes for changes to propagate

### "origin_mismatch" Error
- Ensure JavaScript origins are correctly configured
- Verify the domain is accessible and SSL is working

### Authentication Not Working
- Clear browser cache and cookies
- Try incognito/private browsing mode
- Check browser console for errors

---

**Note**: Complete this OAuth update after your domain DNS is fully propagated and SSL certificate is active.
