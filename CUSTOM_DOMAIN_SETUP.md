# Custom Domain Setup Guide: talktoora.com

This guide will help you set up your custom domain `talktoora.com` to point to your ORA application.

## Prerequisites

- ✅ Domain purchased on GoDaddy: `talktoora.com`
- ✅ ORA application deployed to Google Cloud Run
- ✅ Access to GoDaddy DNS management
- ✅ Google Cloud Console access

## Step-by-Step Setup

### Step 1: Verify Domain in Google Search Console

1. **Go to Google Search Console**
   - Visit: https://search.google.com/search-console
   - Sign in with your Google account (same one used for GCP)

2. **Add Your Domain**
   - Click "Add Property"
   - Select "Domain" (not URL prefix)
   - Enter: `talktoora.com`
   - Click "Continue"

3. **Get Verification TXT Record**
   - Google will provide a TXT record like: `google-site-verification=abc123...`
   - Keep this tab open - you'll need this record

### Step 2: Add DNS TXT Record in GoDaddy

1. **Log into GoDaddy**
   - Go to: https://account.godaddy.com/
   - Navigate to "My Products" → "All Products and Services"

2. **Access DNS Management**
   - Find `talktoora.com` in your domains list
   - Click "DNS" or "Manage DNS"

3. **Add TXT Record**
   - Click "Add" or "Add Record"
   - **Type**: TXT
   - **Name**: @ (or leave blank)
   - **Value**: Paste the verification string from Google Search Console
   - **TTL**: 1 hour (or 3600 seconds)
   - Click "Save"

### Step 3: Verify Domain

1. **Wait for DNS Propagation**
   - Wait 5-15 minutes for the TXT record to propagate

2. **Complete Verification**
   - Go back to Google Search Console
   - Click "Verify"
   - If successful, you'll see "Ownership verified"

### Step 4: Run Domain Setup Script

Once your domain is verified, run our automated setup script:

```bash
# From your ORA project directory
./scripts/setup-custom-domain.sh
```

This script will:
- Create the domain mapping in Cloud Run
- Get the DNS A records you need
- Update CORS settings
- Provide next steps

### Step 5: Add DNS A Records in GoDaddy

The script will provide you with A records like:

```
Record Type: A
Name: @
Value: *************
TTL: 1 hour
```

**Add these to GoDaddy:**
1. In GoDaddy DNS management
2. Click "Add" → "A Record"
3. **Name**: @ (for root domain)
4. **Value**: The IP address provided by the script
5. **TTL**: 1 hour
6. Click "Save"

### Step 6: Wait for SSL Certificate

- Google Cloud Run automatically provisions SSL certificates
- This takes 5-15 minutes after DNS propagation
- You can monitor progress in Cloud Console

## Verification Commands

```bash
# Check domain mapping status
gcloud beta run domain-mappings describe talktoora.com --region=us-central1

# Check DNS propagation
nslookup talktoora.com

# Test your domain (after setup)
curl -I https://talktoora.com
```

## Expected Timeline

- **DNS TXT Record**: 5-15 minutes to propagate
- **Domain Verification**: Immediate after TXT propagation
- **DNS A Records**: 5-15 minutes to propagate
- **SSL Certificate**: 5-15 minutes after A record propagation
- **Total Time**: 15-45 minutes

## Troubleshooting

### Domain Not Verified
- Check TXT record in GoDaddy DNS
- Wait longer for DNS propagation
- Use `nslookup -type=TXT talktoora.com` to verify

### SSL Certificate Issues
- Ensure A records are correctly added
- Wait for full DNS propagation
- Check Cloud Console for certificate status

### CORS Errors
- The script automatically updates CORS settings
- If issues persist, manually update backend environment variables

## Final Configuration

After successful setup:

- **Main Domain**: https://talktoora.com
- **Chat Interface**: https://talktoora.com/chat
- **Admin Panel**: https://talktoora.com/admin
- **Original URL**: Still works as backup

## Support

If you encounter issues:
1. Check the Cloud Console: https://console.cloud.google.com/run/domains
2. Review DNS settings in GoDaddy
3. Check the troubleshooting section above
4. Contact support if needed

---

**Note**: Keep your original Cloud Run URL as a backup during the transition period.
