import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useChat } from '../contexts/ChatContext'
import { VoiceBackground } from '../components/ui/AnimatedBackground'
import VoiceControls from '../components/voice/VoiceControls'
import VoiceOrb from '../components/voice/VoiceOrb'
import VoiceStatus from '../components/voice/VoiceStatus'
import { X, Settings } from 'lucide-react'

export default function VoiceInterfacePage() {
  const { user } = useAuth()
  const { 
    isConnected, 
    isConnecting, 
    isChatActive, 
    messages, 
    connect, 
    startChat,
    endChat,
    isRecording,
    isPlaying
  } = useChat()
  const navigate = useNavigate()
  const [showSettings, setShowSettings] = useState(false)

  // Auto-connect and start chat when component mounts
  useEffect(() => {
    const initializeVoiceMode = async () => {
      try {
        if (!isConnected && !isConnecting) {
          await connect()
        }
        // Only start chat if we're connected and not already active/starting
        if (isConnected && !isChatActive && !isConnecting) {
          startChat()
        }
      } catch (error) {
        console.error('Failed to initialize voice mode:', error)
      }
    }

    initializeVoiceMode()
  }, [isConnected, isConnecting, isChatActive]) // Removed function dependencies to prevent re-runs

  // Get current emotions for background
  const latestEmotions = messages.length > 0 
    ? messages[messages.length - 1]?.emotions || {}
    : {}

  const handleExit = () => {
    if (isChatActive) {
      endChat()
    }
    navigate('/')
  }

  const handleSettingsToggle = () => {
    setShowSettings(!showSettings)
  }

  return (
    <VoiceBackground>
      <div className="min-h-screen flex flex-col relative">
        {/* Header */}
        <header className="absolute top-0 left-0 right-0 z-20 p-4">
          <div className="flex items-center justify-between">
            {/* Exit Button */}
            <button
              onClick={handleExit}
              className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
              title="Exit voice mode"
            >
              <X className="w-5 h-5 text-white" />
            </button>

            {/* Voice Mode Title */}
            <div className="text-center">
              <h1 className="text-white font-medium">Voice Mode</h1>
              <p className="text-white/70 text-sm">
                {user?.profileData?.firstName || user?.name?.split(' ')[0]}
              </p>
            </div>

            {/* Settings Button */}
            <button
              onClick={handleSettingsToggle}
              className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
              title="Voice settings"
            >
              <Settings className="w-5 h-5 text-white" />
            </button>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 flex flex-col items-center justify-center p-8">
          {/* Voice Status */}
          <div className="mb-8">
            <VoiceStatus 
              isConnected={isConnected}
              isConnecting={isConnecting}
              isChatActive={isChatActive}
              isRecording={isRecording}
              isPlaying={isPlaying}
            />
          </div>

          {/* Voice Orb - Main Interface Element */}
          <div className="mb-12">
            <VoiceOrb
              emotions={latestEmotions}
              isListening={isRecording}
              isProcessing={isChatActive && !isRecording && !isPlaying}
              isSpeaking={isPlaying}
              isIdle={!isChatActive}
              intensity={0.9}
              size="large"
            />
          </div>

          {/* Voice Controls */}
          <div className="mb-8">
            <VoiceControls 
              onEndCall={handleExit}
              isRecording={isRecording}
              isPlaying={isPlaying}
            />
          </div>

          {/* Instructions */}
          <div className="text-center max-w-md">
            <p className="text-white/80 text-lg mb-2">
              {isRecording 
                ? "I'm listening..." 
                : isPlaying 
                ? "Speaking..." 
                : "Speak naturally"
              }
            </p>
            <p className="text-white/60 text-sm">
              {isRecording 
                ? "Take your time, I'll wait for you to finish"
                : isPlaying 
                ? "Processing your message with empathy"
                : "I'll respond when you're done speaking"
              }
            </p>
          </div>
        </main>

        {/* Settings Panel */}
        {showSettings && (
          <div className="absolute inset-0 z-30 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl p-6 max-w-md w-full">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-eclipse-950">Voice Settings</h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200"
                >
                  <X className="w-4 h-4 text-gray-600" />
                </button>
              </div>

              {/* Voice Selection */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-eclipse-950 mb-2">
                    Voice Selection
                  </label>
                  <select className="input">
                    <option value="default">Default Voice</option>
                    <option value="warm">Warm & Friendly</option>
                    <option value="professional">Professional</option>
                    <option value="calm">Calm & Soothing</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-eclipse-950 mb-2">
                    Speaking Speed
                  </label>
                  <input 
                    type="range" 
                    min="0.5" 
                    max="2" 
                    step="0.1" 
                    defaultValue="1"
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-eclipse-950/60 mt-1">
                    <span>Slower</span>
                    <span>Normal</span>
                    <span>Faster</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-eclipse-950 mb-2">
                    Sensitivity
                  </label>
                  <input 
                    type="range" 
                    min="1" 
                    max="10" 
                    step="1" 
                    defaultValue="5"
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-eclipse-950/60 mt-1">
                    <span>Less sensitive</span>
                    <span>More sensitive</span>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <button
                onClick={() => setShowSettings(false)}
                className="w-full btn-primary mt-6"
              >
                Save Settings
              </button>
            </div>
          </div>
        )}
      </div>
    </VoiceBackground>
  )
}
