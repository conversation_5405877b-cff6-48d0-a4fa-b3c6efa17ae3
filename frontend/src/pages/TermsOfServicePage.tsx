import React from 'react'
import { ArrowLeft } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export default function TermsOfServicePage() {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-gradient-to-br from-eclipse-900 via-aura-900 to-eclipse-800">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center space-x-2 text-white/70 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back</span>
          </button>
        </div>

        {/* Content */}
        <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20">
          <h1 className="text-4xl font-bold text-white mb-2">Terms of Service</h1>
          <p className="text-white/70 mb-8">Last updated: {new Date().toLocaleDateString()}</p>

          <div className="prose prose-invert max-w-none">
            <div className="space-y-6 text-white/90 leading-relaxed">
              
              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Acceptance of Terms</h2>
                <p>
                  By accessing and using ORA, you accept and agree to be bound by the terms and provision of this agreement.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Description of Service</h2>
                <p>
                  ORA is an empathic voice interface application that provides AI-powered emotional intelligence and 
                  conversational experiences. The service uses advanced emotion detection technology to create 
                  personalized and empathetic interactions.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">User Responsibilities</h2>
                <div className="space-y-3">
                  <p><strong>Appropriate Use:</strong> Use the service for its intended purpose of empathic conversation and emotional support.</p>
                  <p><strong>Accurate Information:</strong> Provide accurate and truthful information during registration and use.</p>
                  <p><strong>Privacy Respect:</strong> Do not share sensitive personal information of others during conversations.</p>
                  <p><strong>Legal Compliance:</strong> Use the service in compliance with all applicable laws and regulations.</p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Service Limitations</h2>
                <div className="space-y-3">
                  <p><strong>Not Medical Advice:</strong> ORA is not a substitute for professional medical, psychological, or therapeutic advice.</p>
                  <p><strong>AI Limitations:</strong> The service uses AI technology which may have limitations and occasional inaccuracies.</p>
                  <p><strong>Availability:</strong> We strive for high availability but cannot guarantee uninterrupted service.</p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Intellectual Property</h2>
                <p>
                  All content, features, and functionality of ORA are owned by us and are protected by copyright, 
                  trademark, and other intellectual property laws.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Privacy and Data</h2>
                <p>
                  Your privacy is important to us. Please review our Privacy Policy to understand how we collect, 
                  use, and protect your information.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Limitation of Liability</h2>
                <p>
                  ORA shall not be liable for any indirect, incidental, special, consequential, or punitive damages 
                  resulting from your use of the service.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Termination</h2>
                <p>
                  We may terminate or suspend your access to the service immediately, without prior notice, 
                  for any reason whatsoever, including breach of these Terms.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Changes to Terms</h2>
                <p>
                  We reserve the right to modify these terms at any time. We will notify users of any material 
                  changes by posting the new Terms of Service on this page.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-white mb-4">Contact Information</h2>
                <p>
                  For questions about these Terms of Service, please contact us at: 
                  <a href="mailto:<EMAIL>" className="text-sunbeam-400 hover:text-sunbeam-300 ml-1">
                    <EMAIL>
                  </a>
                </p>
              </section>

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
