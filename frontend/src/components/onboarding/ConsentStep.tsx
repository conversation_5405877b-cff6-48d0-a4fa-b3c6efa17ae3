import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useOnboarding } from '../../contexts/OnboardingContext'
import { Check } from 'lucide-react'
import { LoadingSpinner } from '../ui/LoadingSpinner'

export default function ConsentStep() {
  const { updateData, onboardingData, submitOnboarding, isLoading, error } = useOnboarding()
  const navigate = useNavigate()
  const [agreeToTerms, setAgreeToTerms] = useState(
    onboardingData.consent?.privacyPolicy || false
  )

  const handleConsentChange = (value: boolean) => {
    setAgreeToTerms(value)
    updateData({
      consent: {
        privacyPolicy: value,
        dataProcessing: false,
        emotionAnalysis: false
      }
    })
  }

  const handleSubmit = async () => {
    try {
      await submitOnboarding()
      // Navigate to chat on success
      navigate('/chat', { replace: true })
    } catch (err) {
      // Error is handled by the context
      console.error('Onboarding submission failed:', err)
    }
  }

  const canProceed = agreeToTerms

  return (
    <div className="text-center space-y-8 max-w-2xl mx-auto">
      {/* Header */}
      <div className="space-y-6">
        <h2 className="text-4xl font-light text-white leading-tight">
          Privacy Consent
        </h2>
      </div>

      {/* Terms Section */}
      <div className="bg-white/10 rounded-2xl p-6 text-left border border-white/20">
        <h3 className="text-xl font-medium text-white mb-4">Terms</h3>
        <div className="bg-white/5 rounded-xl p-4 h-64 overflow-y-auto border border-white/10">
          <div className="text-sm text-white/70 leading-relaxed space-y-4">
            <p>
              <strong>Data Collection:</strong> ORA collects your voice recordings, conversation transcripts, demographic information (name, age), and emotional analysis data from your interactions. We use Google SSO for secure authentication.
            </p>
            <p>
              <strong>Voice Processing:</strong> Your voice data is processed using Hume AI's technology to detect emotions and provide empathetic responses. Voice recordings are stored securely in Google Cloud Storage for service improvement and personalization.
            </p>
            <p>
              <strong>Data Storage:</strong> We store your data indefinitely to provide continuous service improvement. All data is encrypted and stored securely in compliance with industry standards. You may request data deletion at any time.
            </p>
            <p>
              <strong>Third-Party Services:</strong> We use Google (authentication, cloud storage), Hume AI (emotion processing), and analytics services. These partners are bound by strict data protection agreements.
            </p>
            <p>
              <strong>Your Rights:</strong> You have the right to access, modify, or delete your personal data. You can withdraw consent at any time. For privacy requests, contact us through the app settings or support channels.
            </p>
          </div>
        </div>

        {/* Consent Checkbox */}
        <div className="flex items-center space-x-3 mt-6">
          <button
            onClick={() => handleConsentChange(!agreeToTerms)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              agreeToTerms
                ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 border-transparent'
                : 'border-white/30 hover:border-white/50'
            }`}
          >
            {agreeToTerms && <Check className="w-4 h-4 text-white" />}
          </button>
          <p className="text-white font-medium">I agree to the terms</p>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-500/20 border border-red-400/30 rounded-2xl p-4">
          <p className="text-red-300 text-sm flex items-center space-x-2">
            <span>⚠️</span>
            <span>{error}</span>
          </p>
        </div>
      )}

      {/* Continue button */}
      <div className="pt-4">
        <button
          onClick={handleSubmit}
          disabled={!canProceed || isLoading}
          className={`w-full py-4 px-6 rounded-2xl text-lg font-medium transition-opacity ${
            canProceed && !isLoading
              ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white hover:opacity-90'
              : 'bg-white/20 text-white/50 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <LoadingSpinner size="sm" />
              <span>Setting up...</span>
            </div>
          ) : (
            'Continue'
          )}
        </button>
      </div>
    </div>
  )
}
