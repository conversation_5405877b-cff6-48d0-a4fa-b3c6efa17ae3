import { createContext, useContext, useEffect, useState, useRef, ReactNode } from 'react'
import { webSocketService } from '../services/websocket'
import { audioService } from '../services/audio'
import { useAuth } from './AuthContext'
import type { WebSocketMessage } from '@shared/types'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  emotions?: Record<string, number>
  metadata?: any
}

interface ChatContextType {
  // Connection state
  isConnected: boolean
  isConnecting: boolean
  connectionError: string | null

  // Chat state
  isChatActive: boolean
  currentSessionId: string | null
  messages: ChatMessage[]

  // Audio state
  isRecording: boolean
  isPlaying: boolean

  // Button state tracking
  isStartingChat: boolean
  isEndingChat: boolean

  // Actions
  connect: () => Promise<void>
  disconnect: () => void
  startChat: (resumedChatGroupId?: string) => Promise<void>
  endChat: () => void
  sendAudioInput: (audioData: string) => void
  clearMessages: () => void

  // Events
  onMessage: (handler: (message: WebSocketMessage) => void) => void
  offMessage: (handler: (message: WebSocketMessage) => void) => void
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

interface ChatProviderProps {
  children: ReactNode
}

export function ChatProvider({ children }: ChatProviderProps) {
  const { user } = useAuth()

  // Connection state
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)

  // Chat state
  const [isChatActive, setIsChatActive] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])

  // Audio state
  const [isRecording, setIsRecording] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)

  // Connection state tracking to prevent multiple simultaneous connections
  const [isStartingChat, setIsStartingChat] = useState(false)
  const [isEndingChat, setIsEndingChat] = useState(false)

  // Use ref to track chat active state for immediate access in callbacks
  const isChatActiveRef = useRef(false)
  // Use ref to track starting state for immediate access to prevent race conditions
  const isStartingChatRef = useRef(false)

  // Update recording state based on audio service
  useEffect(() => {
    const updateRecordingState = () => {
      setIsRecording(audioService.getIsRecording())
    }

    // Check recording state periodically
    const interval = setInterval(updateRecordingState, 100)

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    if (user) {
      setupWebSocketHandlers()
    } else {
      disconnect()
    }

    return () => {
      // Clean up event handlers before disconnecting
      cleanupWebSocketHandlers()
      webSocketService.disconnect()
    }
  }, [user])

  // Sync connection state periodically to catch any mismatches
  useEffect(() => {
    const syncInterval = setInterval(() => {
      const actuallyConnected = webSocketService.isConnected
      if (actuallyConnected !== isConnected) {
        console.log(`🔄 Syncing connection state: ${isConnected} -> ${actuallyConnected}`)
        setIsConnected(actuallyConnected)
      }
    }, 1000)

    return () => clearInterval(syncInterval)
  }, [isConnected])

  const setupWebSocketHandlers = () => {
    // Clean up any existing handlers first to prevent duplicates
    cleanupWebSocketHandlers()

    // Connection events
    webSocketService.on('connection_established', handleConnectionEstablished)
    webSocketService.on('connection_lost', handleConnectionLost)
    webSocketService.on('connection_error', handleConnectionError)

    // Chat events
    webSocketService.on('chat_started', handleChatStarted)
    webSocketService.on('chat_ended', handleChatEnded)
    webSocketService.on('hume_message', handleHumeMessage)
    webSocketService.on('error', handleError)
  }

  const cleanupWebSocketHandlers = () => {
    // Remove all event handlers to prevent duplicates
    webSocketService.off('connection_established', handleConnectionEstablished)
    webSocketService.off('connection_lost', handleConnectionLost)
    webSocketService.off('connection_error', handleConnectionError)
    webSocketService.off('chat_started', handleChatStarted)
    webSocketService.off('chat_ended', handleChatEnded)
    webSocketService.off('hume_message', handleHumeMessage)
    webSocketService.off('error', handleError)
  }

  const handleConnectionEstablished = (message: WebSocketMessage) => {
    console.log('✅ Chat connection established', message)
    console.log('🔄 Updating ChatContext state: isConnected = true')
    setIsConnected(true)
    setIsConnecting(false)
    setConnectionError(null)
  }

  const handleConnectionLost = (_message: WebSocketMessage) => {
    console.log('🔌 Chat connection lost')
    setIsConnected(false)
    setIsConnecting(false)
    setIsChatActive(false)
    setCurrentSessionId(null)
  }

  const handleConnectionError = (message: WebSocketMessage) => {
    console.error('❌ Chat connection error:', message.data)
    setConnectionError(message.data?.error || 'Connection error')
    setIsConnecting(false)
  }

  const handleChatStarted = async (message: WebSocketMessage) => {
    console.log('🎯 Chat started:', message.data)

    // Update both state and ref immediately
    setIsChatActive(true)
    isChatActiveRef.current = true
    setCurrentSessionId(message.data?.sessionId || null)
    setMessages([])

    // Reset starting state since chat is now active
    setIsStartingChat(false)
    isStartingChatRef.current = false

    // Wait a moment for the backend to fully establish the Hume connection
    await new Promise(resolve => setTimeout(resolve, 500))

    // Now start audio recording since chat is active
    try {
      console.log('🎤 Starting audio recording after chat started...')
      await audioService.startRecording((audioData: string) => {
        console.log('🎙️ Audio data received in callback, length:', audioData.length)
        // Use ref for immediate access to current state
        if (isChatActiveRef.current) {
          sendAudioInput(audioData)
        } else {
          console.warn('⚠️ Chat no longer active, skipping audio data')
        }
      })
      console.log('✅ Audio recording started successfully')
    } catch (error) {
      console.error('Failed to start audio recording:', error)
    }
  }

  const handleChatEnded = (message: WebSocketMessage) => {
    console.log('🏁 Chat ended:', message.data)

    // Update both state and ref immediately
    setIsChatActive(false)
    isChatActiveRef.current = false
    setCurrentSessionId(null)
    setIsRecording(false)
    setIsPlaying(false)

    // Reset button states
    setIsStartingChat(false)
    isStartingChatRef.current = false
    setIsEndingChat(false)

    // Stop all audio - both recording and playback
    audioService.stopRecording()
    audioService.stopPlayback()
  }

  const handleHumeMessage = (message: WebSocketMessage) => {
    const humeMessage = message.data
    console.log('🤖 Hume message:', humeMessage.type)

    switch (humeMessage.type) {
      case 'user_message':
        addMessage({
          id: humeMessage.message?.id || Date.now().toString(),
          role: 'user',
          content: humeMessage.message?.content || '',
          timestamp: new Date(humeMessage.timestamp || Date.now()),
          emotions: extractEmotions(humeMessage),
          metadata: humeMessage
        })
        setIsRecording(false)
        // Stop audio playback when user speaks
        audioService.stopPlayback()
        break

      case 'assistant_message':
        addMessage({
          id: humeMessage.message?.id || Date.now().toString(),
          role: 'assistant',
          content: humeMessage.message?.content || '',
          timestamp: new Date(humeMessage.timestamp || Date.now()),
          emotions: extractEmotions(humeMessage), // Extract assistant emotions too!
          metadata: humeMessage
        })
        break

      case 'audio_output':
        setIsPlaying(true)
        // Play audio using the audio service
        audioService.playAudio(humeMessage)
        break

      case 'user_interruption':
        setIsPlaying(false)
        // Stop audio playback when user interrupts
        audioService.stopPlayback()
        break

      case 'tool_call':
        addMessage({
          id: Date.now().toString(),
          role: 'assistant',
          content: `[Tool Call: ${humeMessage.name}]`,
          timestamp: new Date(),
          metadata: humeMessage
        })
        break
    }
  }

  const handleError = (message: WebSocketMessage) => {
    console.error('💥 Chat error:', message.data)
    setConnectionError(message.data?.message || 'An error occurred')
  }

  const extractEmotions = (humeMessage: any): Record<string, number> => {
    const emotions: Record<string, number> = {}

    const messageType = humeMessage.type || 'unknown'
    console.log(`🔍 Frontend extracting emotions from ${messageType}:`, humeMessage.models ? Object.keys(humeMessage.models) : 'no models')

    // Extract from prosody scores (voice-based emotions)
    if (humeMessage.models?.prosody?.scores) {
      console.log(`🎵 Frontend found prosody scores:`, Object.keys(humeMessage.models.prosody.scores))
      Object.entries(humeMessage.models.prosody.scores).forEach(([emotion, score]) => {
        emotions[emotion] = score as number
      })
    }

    // Extract from language/utterance-based emotions if available
    if (humeMessage.models?.language?.scores) {
      console.log(`💬 Frontend found language scores:`, Object.keys(humeMessage.models.language.scores))
      Object.entries(humeMessage.models.language.scores).forEach(([emotion, score]) => {
        const numScore = score as number
        // Combine with prosody scores, giving slight preference to language-based emotions
        emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore * 1.1) / 2 : numScore
      })
    }

    // Check for face-based emotions
    if (humeMessage.models?.face?.scores) {
      console.log(`😊 Frontend found face scores:`, Object.keys(humeMessage.models.face.scores))
      Object.entries(humeMessage.models.face.scores).forEach(([emotion, score]) => {
        const numScore = score as number
        emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore) / 2 : numScore
      })
    }

    // Check for any other emotion model types
    if (humeMessage.models) {
      Object.keys(humeMessage.models).forEach(modelType => {
        if (modelType !== 'prosody' && modelType !== 'language' && modelType !== 'face') {
          const modelData = humeMessage.models[modelType]
          if (modelData?.scores) {
            console.log(`🔍 Frontend found ${modelType} scores:`, Object.keys(modelData.scores))
            Object.entries(modelData.scores).forEach(([emotion, score]) => {
              const numScore = score as number
              emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore) / 2 : numScore
            })
          }
        }
      })
    }

    console.log(`✅ Frontend final ${messageType} emotions:`, Object.keys(emotions).length, 'emotions found')
    console.log(`🎯 Frontend top ${messageType} emotions:`, Object.entries(emotions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`)
      .join(', '))

    return emotions
  }

  const addMessage = (message: ChatMessage) => {
    setMessages(prev => [...prev, message])
  }

  const connect = async () => {
    if (!user) {
      throw new Error('User must be authenticated to connect')
    }

    try {
      setIsConnecting(true)
      setConnectionError(null)
      await webSocketService.connect()
    } catch (error) {
      console.error('Failed to connect to chat:', error)
      setConnectionError(error instanceof Error ? error.message : 'Connection failed')
      setIsConnecting(false)
      throw error
    }
  }

  const disconnect = () => {
    webSocketService.disconnect()
    setIsConnected(false)
    setIsConnecting(false)
    setIsChatActive(false)
    isChatActiveRef.current = false
    setCurrentSessionId(null)
    setMessages([])
    setIsRecording(false)
    setIsPlaying(false)
  }

  const startChat = async (resumedChatGroupId?: string) => {
    if (!isConnected) {
      throw new Error('Not connected to chat server')
    }

    // Prevent multiple simultaneous start attempts using both state and ref for immediate protection
    if (isChatActive || isConnecting || isStartingChat || isStartingChatRef.current) {
      console.warn('⚠️ Chat already active, connecting, or starting - ignoring start request')
      return
    }

    // Additional check: if we already have a session ID, don't start again
    if (currentSessionId) {
      console.warn('⚠️ Chat session already exists - ignoring start request')
      return
    }

    // Immediately set starting state to prevent race conditions
    setIsStartingChat(true)
    isStartingChatRef.current = true

    try {
      console.log('🎬 Starting chat with audio...')

      // Initialize audio service (but don't start recording yet)
      console.log('🎵 Initializing audio service...')
      await audioService.init()

      // Start chat session - audio recording will start when chat_started is received
      console.log('💬 Starting WebSocket chat session...')
      webSocketService.startChat(resumedChatGroupId)
    } catch (error) {
      console.error('Failed to start chat with audio:', error)
      // Reset starting state on error
      setIsStartingChat(false)
      isStartingChatRef.current = false
      throw error
    }
  }

  const endChat = () => {
    // Prevent multiple simultaneous end attempts
    if (!isChatActive || isEndingChat) {
      console.warn('⚠️ No active chat to end or already ending, ignoring end request')
      return
    }

    console.log('🏁 Ending chat immediately...')

    // Set ending state to prevent multiple calls
    setIsEndingChat(true)

    // Immediately update state to prevent multiple calls and update UI
    setIsChatActive(false)
    isChatActiveRef.current = false
    setIsRecording(false)
    setIsPlaying(false)

    // Stop all audio immediately - both recording and playback
    audioService.stopRecording()
    audioService.stopPlayback()

    // End chat session on server (but don't wait for response)
    webSocketService.endChat()

    console.log('✅ Chat ended locally immediately')

    // Reset ending state after a short delay
    setTimeout(() => {
      setIsEndingChat(false)
    }, 1000)
  }

  const sendAudioInput = (audioData: string) => {
    console.log('🎙️ ChatContext sendAudioInput called, data length:', audioData.length)

    const actuallyConnected = webSocketService.isConnected
    console.log('🎙️ Chat state:', {
      isConnected,
      actuallyConnected,
      isChatActive: isChatActiveRef.current,
      currentSessionId,
      wsConnectionState: webSocketService.connectionState
    })

    // Use the actual WebSocket connection state instead of the ChatContext state
    if (!actuallyConnected) {
      console.error('❌ WebSocket not actually connected')
      return // Don't throw, just skip silently
    }

    if (!isChatActiveRef.current) {
      console.error('❌ No active chat session for audio input')
      return // Don't throw, just skip silently
    }

    try {
      webSocketService.sendAudioInput(audioData)
    } catch (error) {
      console.error('❌ Failed to send audio input:', error)
      // Don't throw here either, as this would break the audio recording loop
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  const onMessage = (handler: (message: WebSocketMessage) => void) => {
    webSocketService.on('*', handler)
  }

  const offMessage = (handler: (message: WebSocketMessage) => void) => {
    webSocketService.off('*', handler)
  }

  const value: ChatContextType = {
    // Connection state
    isConnected,
    isConnecting,
    connectionError,

    // Chat state
    isChatActive,
    currentSessionId,
    messages,

    // Audio state
    isRecording,
    isPlaying,

    // Button state tracking
    isStartingChat,
    isEndingChat,

    // Actions
    connect,
    disconnect,
    startChat,
    endChat,
    sendAudioInput,
    clearMessages,

    // Events
    onMessage,
    offMessage
  }

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  )
}

export function useChat() {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}
