FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci

# Copy shared types
COPY shared ../shared

# Copy frontend source code
COPY frontend .

# Set environment variables for build
ENV VITE_API_URL=https://ora-backend-222129249954.us-central1.run.app
ENV VITE_WS_URL=wss://ora-backend-222129249954.us-central1.run.app
ENV VITE_GOOGLE_CLIENT_ID=222129249954-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com
ENV VITE_HUME_API_KEY=ofPMZlMGpzwv1FkzY5LRVpFksxTlobYrEiAqG0AK9QYPfrmK
ENV VITE_APP_NAME=ORA
ENV VITE_APP_VERSION=1.0.0

# Build the application
RUN npm run build

# Install serve to serve static files
RUN npm install -g serve

# Expose port (Cloud Run uses PORT env var)
EXPOSE $PORT

# Start the application
CMD serve -s dist -l $PORT
