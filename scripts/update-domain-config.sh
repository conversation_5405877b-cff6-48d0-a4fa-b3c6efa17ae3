#!/bin/bash

# Update ORA configuration for custom domain
# This script updates deployment files to support talktoora.com

set -e

echo "🔧 Updating ORA configuration for custom domain"
echo "==============================================="

CUSTOM_DOMAIN="talktoora.com"
ORIGINAL_FRONTEND="https://ora-frontend-222129249954.us-central1.run.app"
BACKEND_URL="https://ora-backend-222129249954.us-central1.run.app"

echo "📝 Configuration:"
echo "   Custom Domain: https://$CUSTOM_DOMAIN"
echo "   Original Frontend: $ORIGINAL_FRONTEND"
echo "   Backend URL: $BACKEND_URL"
echo ""

# Update cloudbuild.yaml to include both domains in CORS
echo "🔄 Updating cloudbuild.yaml..."

# Create backup
cp cloudbuild.yaml cloudbuild.yaml.backup

# Update CORS origins to include both domains
sed -i.tmp "s|CORS_ORIGINS=https://ora-frontend-222129249954.us-central1.run.app|CORS_ORIGINS=https://ora-frontend-222129249954.us-central1.run.app,https://$CUSTOM_DOMAIN|g" cloudbuild.yaml

# Update frontend URL to use custom domain as primary
sed -i.tmp "s|FRONTEND_URL=https://ora-frontend-222129249954.us-central1.run.app|FRONTEND_URL=https://$CUSTOM_DOMAIN|g" cloudbuild.yaml

# Clean up temp files
rm -f cloudbuild.yaml.tmp

echo "✅ Updated cloudbuild.yaml"

# Update production environment files
echo "🔄 Updating production environment files..."

# Update backend production env
if [ -f "backend/.env.production" ]; then
    cp backend/.env.production backend/.env.production.backup
    
    # Update FRONTEND_URL and CORS_ORIGINS
    sed -i.tmp "s|FRONTEND_URL=.*|FRONTEND_URL=https://$CUSTOM_DOMAIN|g" backend/.env.production
    sed -i.tmp "s|CORS_ORIGINS=.*|CORS_ORIGINS=$ORIGINAL_FRONTEND,https://$CUSTOM_DOMAIN|g" backend/.env.production
    
    rm -f backend/.env.production.tmp
    echo "✅ Updated backend/.env.production"
fi

# Update frontend production env
if [ -f "frontend/.env.production" ]; then
    cp frontend/.env.production frontend/.env.production.backup
    
    # Add custom domain info (for reference)
    echo "" >> frontend/.env.production
    echo "# Custom Domain Configuration" >> frontend/.env.production
    echo "# Primary Domain: https://$CUSTOM_DOMAIN" >> frontend/.env.production
    echo "# Backup Domain: $ORIGINAL_FRONTEND" >> frontend/.env.production
    
    echo "✅ Updated frontend/.env.production"
fi

# Update Dockerfile environment variables
echo "🔄 Updating Dockerfile environment variables..."

if [ -f "frontend/Dockerfile" ]; then
    cp frontend/Dockerfile frontend/Dockerfile.backup
    
    # Note: Dockerfile uses build-time variables, so we keep the backend URLs the same
    # The custom domain will be handled by Cloud Run domain mapping
    echo "✅ Frontend Dockerfile ready (no changes needed)"
fi

echo ""
echo "🎉 Configuration updated successfully!"
echo ""
echo "📋 Changes made:"
echo "   ✅ cloudbuild.yaml - Updated CORS origins and frontend URL"
echo "   ✅ backend/.env.production - Updated for custom domain"
echo "   ✅ frontend/.env.production - Added domain references"
echo ""
echo "📝 Backup files created:"
echo "   📄 cloudbuild.yaml.backup"
echo "   📄 backend/.env.production.backup"
echo "   📄 frontend/.env.production.backup"
echo ""
echo "🚀 Next steps:"
echo "   1. Run the domain setup script: ./scripts/setup-custom-domain.sh"
echo "   2. Or manually deploy with: gcloud builds submit --config=cloudbuild.yaml"
echo ""
