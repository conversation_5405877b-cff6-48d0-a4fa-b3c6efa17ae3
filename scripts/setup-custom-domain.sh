#!/bin/bash

# ORA Custom Domain Setup Script
# This script helps set up talktoora.com to point to your ORA application

set -e

echo "🌐 ORA Custom Domain Setup"
echo "=========================="
echo ""

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Please authenticate with gcloud first:"
    echo "   gcloud auth login"
    exit 1
fi

# Set project
PROJECT_ID="ora-phase1"
DOMAIN="talktoora.com"
REGION="us-central1"

echo "📋 Configuration:"
echo "   Project: $PROJECT_ID"
echo "   Domain: $DOMAIN"
echo "   Region: $REGION"
echo ""

# Set the project
gcloud config set project $PROJECT_ID

echo "🔍 Step 1: Check if domain is verified..."
echo ""
echo "⚠️  IMPORTANT: Before proceeding, you must verify your domain in Google Search Console:"
echo "   1. Go to https://search.google.com/search-console"
echo "   2. Click 'Add Property' → 'Domain'"
echo "   3. Enter: $DOMAIN"
echo "   4. Add the TXT record to your GoDaddy DNS settings"
echo "   5. Wait for verification (5-15 minutes)"
echo ""

read -p "Have you verified $DOMAIN in Google Search Console? (y/N): " verified
if [[ ! $verified =~ ^[Yy]$ ]]; then
    echo "❌ Please verify your domain first, then run this script again."
    exit 1
fi

echo ""
echo "🚀 Step 2: Creating domain mapping for frontend..."

# Create domain mapping for frontend
if gcloud beta run domain-mappings create \
    --service=ora-frontend \
    --domain=$DOMAIN \
    --region=$REGION; then
    echo "✅ Domain mapping created successfully!"
else
    echo "❌ Failed to create domain mapping. Please check if domain is verified."
    exit 1
fi

echo ""
echo "📝 Step 3: Getting DNS records for GoDaddy..."

# Get the domain mapping details
MAPPING_INFO=$(gcloud beta run domain-mappings describe $DOMAIN --region=$REGION --format="value(status.resourceRecords[].name,status.resourceRecords[].rrdata)" 2>/dev/null || echo "")

if [ -n "$MAPPING_INFO" ]; then
    echo ""
    echo "🎯 DNS Records to add in GoDaddy:"
    echo "================================="
    echo ""
    
    # Parse and display the records
    while IFS=$'\t' read -r name rrdata; do
        if [[ $name == *"$DOMAIN"* ]]; then
            # Extract subdomain from full domain name
            subdomain=${name%.$DOMAIN.}
            if [ "$subdomain" = "$DOMAIN" ]; then
                subdomain="@"
            fi
            
            echo "Record Type: A"
            echo "Name: $subdomain"
            echo "Value: $rrdata"
            echo "TTL: 1 hour"
            echo ""
        fi
    done <<< "$MAPPING_INFO"
    
    echo "📋 Instructions for GoDaddy:"
    echo "1. Log into your GoDaddy account"
    echo "2. Go to 'My Products' → 'DNS' for $DOMAIN"
    echo "3. Add the A record(s) shown above"
    echo "4. Wait 5-15 minutes for DNS propagation"
    echo ""
else
    echo "⚠️  Could not retrieve DNS records automatically."
    echo "   Please check the Cloud Console for DNS record details:"
    echo "   https://console.cloud.google.com/run/domains"
fi

echo "🔄 Step 4: Update application configuration..."

# Update environment variables to include the new domain
echo ""
echo "📝 Updating CORS origins to include new domain..."

# Get current backend service details
BACKEND_URL="https://api.talktoora.com"
FRONTEND_URL="https://$DOMAIN"
CORS_ORIGINS="https://ora-frontend-************.us-central1.run.app,https://$DOMAIN"

echo "   Backend URL: $BACKEND_URL"
echo "   Frontend URL: $FRONTEND_URL"
echo "   CORS Origins: $CORS_ORIGINS"

# Update backend service with new CORS origins
if gcloud run services update ora-backend \
    --region=$REGION \
    --set-env-vars="NODE_ENV=production,BACKEND_URL=$BACKEND_URL,FRONTEND_URL=$FRONTEND_URL,CORS_ORIGINS=\"$CORS_ORIGINS\",DEFAULT_ADMIN_EMAIL=<EMAIL>"; then
    echo "✅ Backend service updated with new CORS origins"
else
    echo "⚠️  Failed to update backend service. You may need to update CORS manually."
fi

echo ""
echo "🎉 Domain setup initiated!"
echo ""
echo "📋 Next Steps:"
echo "1. Add the DNS A records to GoDaddy (shown above)"
echo "2. Wait 5-15 minutes for DNS propagation"
echo "3. Test your domain: https://$DOMAIN"
echo "4. SSL certificate will be automatically provisioned"
echo ""
echo "🔍 To check status:"
echo "   gcloud beta run domain-mappings describe $DOMAIN --region=$REGION"
echo ""
echo "📊 To monitor SSL certificate:"
echo "   Watch the 'Certificate Status' in Cloud Console"
echo "   https://console.cloud.google.com/run/domains"
echo ""
