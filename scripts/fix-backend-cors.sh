#!/bin/bash

# Fix Backend CORS Configuration for Custom Domain
# This script properly updates the backend service with CORS origins

set -e

echo "🔧 Fixing Backend CORS Configuration"
echo "===================================="

PROJECT_ID="ora-phase1"
REGION="us-central1"
BACKEND_URL="https://ora-backend-222129249954.us-central1.run.app"
FRONTEND_URL="https://talktoora.com"
ORIGINAL_FRONTEND="https://ora-frontend-222129249954.us-central1.run.app"

echo "📝 Configuration:"
echo "   Project: $PROJECT_ID"
echo "   Region: $REGION"
echo "   Backend URL: $BACKEND_URL"
echo "   Frontend URL: $FRONTEND_URL"
echo "   Original Frontend: $ORIGINAL_FRONTEND"
echo ""

# Set the project
gcloud config set project $PROJECT_ID

echo "🔄 Updating backend service with proper CORS configuration..."

# Update backend service with properly escaped CORS origins
# We need to escape the comma-separated values properly
gcloud run services update ora-backend \
    --region=$REGION \
    --update-env-vars="NODE_ENV=production" \
    --update-env-vars="BACKEND_URL=$BACKEND_URL" \
    --update-env-vars="FRONTEND_URL=$FRONTEND_URL" \
    --update-env-vars="CORS_ORIGINS=$ORIGINAL_FRONTEND,$FRONTEND_URL" \
    --update-env-vars="DEFAULT_ADMIN_EMAIL=<EMAIL>"

if [ $? -eq 0 ]; then
    echo "✅ Backend service updated successfully!"
    echo ""
    echo "📋 Updated environment variables:"
    echo "   NODE_ENV=production"
    echo "   BACKEND_URL=$BACKEND_URL"
    echo "   FRONTEND_URL=$FRONTEND_URL"
    echo "   CORS_ORIGINS=$ORIGINAL_FRONTEND,$FRONTEND_URL"
    echo "   DEFAULT_ADMIN_EMAIL=<EMAIL>"
else
    echo "❌ Failed to update backend service"
    echo ""
    echo "🔧 Manual fix required:"
    echo "   1. Go to Google Cloud Console"
    echo "   2. Navigate to Cloud Run > ora-backend"
    echo "   3. Click 'Edit & Deploy New Revision'"
    echo "   4. Update environment variables:"
    echo "      CORS_ORIGINS=$ORIGINAL_FRONTEND,$FRONTEND_URL"
    echo "      FRONTEND_URL=$FRONTEND_URL"
    echo "   5. Deploy the revision"
    exit 1
fi

echo ""
echo "🎉 Backend CORS configuration updated!"
echo ""
echo "📋 Next Steps:"
echo "1. Add the DNS A records to GoDaddy (from previous output)"
echo "2. Wait 5-15 minutes for DNS propagation"
echo "3. Test your domain: https://talktoora.com"
echo ""
echo "🔍 To verify the update:"
echo "   gcloud run services describe ora-backend --region=$REGION --format='value(spec.template.spec.template.spec.containers[0].env[].value)'"
echo ""
