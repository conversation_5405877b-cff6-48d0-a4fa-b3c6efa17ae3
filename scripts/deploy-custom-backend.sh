#!/bin/bash

# Deploy ORA with Custom Backend Domain Integration
# This script deploys the updated configuration to use api.talktoora.com

set -e

echo "🚀 Deploying ORA with Custom Backend Domain"
echo "==========================================="
echo ""

# Configuration
PROJECT_ID="ora-phase1"
REGION="us-central1"
CUSTOM_BACKEND="https://api.talktoora.com"
CUSTOM_FRONTEND="https://talktoora.com"

echo "📋 Configuration:"
echo "   Project: $PROJECT_ID"
echo "   Region: $REGION"
echo "   Backend: $CUSTOM_BACKEND"
echo "   Frontend: $CUSTOM_FRONTEND"
echo ""

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Please authenticate with gcloud first:"
    echo "   gcloud auth login"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

echo "🔍 Step 1: Verify custom domains are accessible..."

# Test custom backend domain
echo "   Testing backend: $CUSTOM_BACKEND"
if curl -s -f "$CUSTOM_BACKEND/health" > /dev/null; then
    echo "   ✅ Backend domain is accessible"
else
    echo "   ❌ Backend domain is not accessible yet"
    echo "   ⚠️  Please ensure api.talktoora.com DNS and SSL are working"
    echo "   💡 You can still deploy, but test after DNS propagation"
fi

# Test custom frontend domain
echo "   Testing frontend: $CUSTOM_FRONTEND"
if curl -s -f "$CUSTOM_FRONTEND" > /dev/null; then
    echo "   ✅ Frontend domain is accessible"
else
    echo "   ❌ Frontend domain is not accessible yet"
    echo "   ⚠️  Please ensure talktoora.com DNS and SSL are working"
fi

echo ""
echo "🏗️  Step 2: Building and deploying application..."

# Deploy using Cloud Build
echo "   Submitting build to Cloud Build..."
if gcloud builds submit --config=cloudbuild.yaml; then
    echo "   ✅ Build and deployment successful!"
else
    echo "   ❌ Build failed. Please check the logs above."
    exit 1
fi

echo ""
echo "🧪 Step 3: Testing deployment..."

# Wait a moment for deployment to be ready
echo "   Waiting for services to be ready..."
sleep 10

# Test backend health
echo "   Testing backend health..."
BACKEND_HEALTH=$(curl -s "$CUSTOM_BACKEND/health" | jq -r '.data.status' 2>/dev/null || echo "error")
if [ "$BACKEND_HEALTH" = "healthy" ]; then
    echo "   ✅ Backend is healthy"
else
    echo "   ⚠️  Backend health check failed or pending"
    echo "   💡 This might be normal if DNS is still propagating"
fi

# Test frontend
echo "   Testing frontend..."
if curl -s -f "$CUSTOM_FRONTEND" > /dev/null; then
    echo "   ✅ Frontend is accessible"
else
    echo "   ⚠️  Frontend not accessible or pending"
fi

echo ""
echo "🔐 Step 4: OAuth configuration reminder..."
echo ""
echo "⚠️  IMPORTANT: Update your Google OAuth configuration:"
echo "   1. Go to: https://console.cloud.google.com/apis/credentials"
echo "   2. Edit your OAuth 2.0 Client ID"
echo "   3. Add these Authorized JavaScript Origins:"
echo "      - https://talktoora.com"
echo "      - https://api.talktoora.com"
echo "   4. Add these Authorized Redirect URIs:"
echo "      - https://api.talktoora.com/auth/callback"
echo "      - https://api.talktoora.com/auth/google/callback"
echo "      - https://api.talktoora.com/api/auth/google/callback"
echo ""
echo "📖 See OAUTH_CUSTOM_BACKEND_UPDATE.md for detailed instructions"

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📋 Next Steps:"
echo "   1. Update OAuth configuration (see above)"
echo "   2. Wait for DNS propagation (5-15 minutes)"
echo "   3. Test authentication flow"
echo "   4. Verify all functionality works"
echo ""
echo "🔗 Your application URLs:"
echo "   Frontend: $CUSTOM_FRONTEND"
echo "   Backend:  $CUSTOM_BACKEND"
echo "   API:      $CUSTOM_BACKEND/api"
echo "   Health:   $CUSTOM_BACKEND/health"
echo ""
echo "🧪 Test commands:"
echo "   curl $CUSTOM_BACKEND/health"
echo "   curl $CUSTOM_FRONTEND"
echo ""
