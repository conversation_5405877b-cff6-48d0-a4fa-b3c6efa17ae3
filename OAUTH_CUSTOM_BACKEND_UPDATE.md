# OAuth Configuration Update for Custom Backend Domain

Now that you have `https://api.talktoora.com` set up for your backend, you need to update your Google OAuth configuration to use this professional domain instead of the Cloud Run URL.

## Step-by-Step OAuth Update

### Step 1: Access Google Cloud Console

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Select project: `ora-phase1`

### Step 2: Update OAuth Consent Screen

1. **Navigate to OAuth Consent Screen**
   - Go to "APIs & Services" → "OAuth consent screen"

2. **Update Application Domain Information**
   ```
   Application home page: https://talktoora.com
   Application privacy policy URL: https://talktoora.com/privacy
   Application terms of service URL: https://talktoora.com/terms
   ```

3. **Update Authorized Domains** (add if not already present)
   ```
   talktoora.com
   us-central1.run.app
   ```

4. **Save Changes**

### Step 3: Update OAuth 2.0 Client Credentials

1. **Go to Credentials**
   - Navigate to "APIs & Services" → "Credentials"

2. **Edit Your OAuth 2.0 Client**
   - Click on: `222129249954-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com`

3. **Update Authorized JavaScript Origins**
   Add these origins (keep existing ones):
   ```
   https://talktoora.com
   https://api.talktoora.com
   https://ora-frontend-222129249954.us-central1.run.app
   ```

4. **Update Authorized Redirect URIs**
   Add these redirect URIs (keep existing ones):
   ```
   https://api.talktoora.com/auth/callback
   https://api.talktoora.com/auth/google/callback
   https://api.talktoora.com/api/auth/google/callback
   https://talktoora.com/auth/callback
   https://talktoora.com/auth/google/callback
   https://ora-frontend-222129249954.us-central1.run.app/auth/callback
   https://ora-frontend-222129249954.us-central1.run.app/auth/google/callback
   ```

5. **Save Changes**

### Step 4: Verify Configuration

Your OAuth client should now have:

**Authorized JavaScript Origins:**
- `https://talktoora.com`
- `https://api.talktoora.com`
- `https://ora-frontend-222129249954.us-central1.run.app`

**Authorized Redirect URIs:**
- `https://api.talktoora.com/auth/callback`
- `https://api.talktoora.com/auth/google/callback`
- `https://api.talktoora.com/api/auth/google/callback`
- `https://talktoora.com/auth/callback`
- `https://talktoora.com/auth/google/callback`
- `https://ora-frontend-222129249954.us-central1.run.app/auth/callback`
- `https://ora-frontend-222129249954.us-central1.run.app/auth/google/callback`

## Expected Results

### Before OAuth Update
```
"You're signing back in to api.talktoora.com"
```

### After OAuth Consent Screen Update
```
"You're signing back in to ORA"
```

## Important Notes

1. **Keep All Domains**: Keep both custom domains and Cloud Run URLs for redundancy during transition
2. **Multiple Callback Paths**: Include both `/auth/callback` and `/api/auth/google/callback` patterns
3. **Propagation Time**: OAuth changes are usually immediate but can take up to 5 minutes
4. **Testing**: Test authentication on all domains to ensure everything works

## Testing Checklist

After making these changes, test the authentication flow:

1. **Custom Frontend + Custom Backend**: https://talktoora.com → https://api.talktoora.com
2. **Original Frontend + Custom Backend**: https://ora-frontend-222129249954.us-central1.run.app → https://api.talktoora.com
3. **Verify OAuth Branding**: Should show "ORA" instead of domain name

## Troubleshooting

### "redirect_uri_mismatch" Error
- Ensure all redirect URIs are added to the OAuth client
- Check that the domain matches exactly (including https://)
- Verify the callback path is correct (`/api/auth/google/callback`)

### "origin_mismatch" Error
- Ensure JavaScript origins are correctly configured
- Verify both frontend and backend domains are listed
- Check that SSL is working on both domains

### Still Seeing Domain Name Instead of "ORA"
- Verify OAuth consent screen has "Application name: ORA"
- Clear browser cache and cookies
- Wait a few minutes for changes to propagate
- Try incognito/private browsing mode

### Authentication Not Working
- Check that `https://api.talktoora.com` is accessible
- Verify SSL certificate is active on the backend domain
- Check browser console for errors
- Ensure environment variables are updated in the deployed application

---

**Next Step**: After completing this OAuth update, deploy your application with the updated environment variables to complete the integration!
